# **App Name**: Alquileres Goya

## Core Features:

- Property Description: Display property descriptions, including details about the accommodation.
- Image Carousel: Display a carousel of images for each property, with swipe functionality on mobile.
- Type Filtering: Allow users to filter properties by type (house, apartment, warehouse, etc.).
- Responsive Layout: Enable responsive design to ensure optimal viewing on both mobile and desktop devices.
- Map Integration: Use a free map service to display property locations.
- AI Review Summarizer: Summarize user reviews and highlight key points relevant to the property, acting as a summarization tool.

## Style Guidelines:

- Primary color: Deep blue (#3F51B5) to convey trust and stability in property listings.
- Background color: Very light blue (#F0F2FA), providing a clean, unobtrusive backdrop.
- Accent color: Purple (#7E57C2), to draw attention to key interactive elements, and maintain the cool tones from the primary hue.
- Clean, modern typography that ensures readability across all devices.
- Simple, intuitive icons that clearly represent different property types and features.
- A grid-based layout for displaying properties, ensuring a consistent and visually appealing presentation.
- Subtle transitions and animations to enhance the user experience when navigating the property listings and details.