/* Import Leaflet CSS (removed) */
/* @import 'leaflet/dist/leaflet.css'; */

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 228 67% 96%; /* #F0F2FA Very Light Blue */
    --foreground: 230 20% 25%; /* Darker Blue/Grey */
    --card: 228 60% 99%;
    --card-foreground: 230 20% 25%;
    --popover: 228 60% 99%;
    --popover-foreground: 230 20% 25%;

    --primary: 175 55% 45%; /* Teal-ish */
    --primary-foreground: 0 0% 100%; /* White */

    --secondary: 230 50% 90%;
    --secondary-foreground: 175 55% 45%; /* Teal-ish for secondary text on light secondary bg */

    --muted: 230 50% 90%;
    --muted-foreground: 230 30% 60%;

    --accent: 30 90% 55%; /* Brighter Orange */
    --accent-foreground: 0 0% 100%; /* White */

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 230 40% 85%;
    --input: 230 40% 90%;
    --ring: 175 55% 45%; /* Teal-ish for ring */

    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;

    /* Sidebar specific theme adjustments - can be further customized if sidebar is complex */
    --sidebar-background: 230 60% 98%;
    --sidebar-foreground: 231 48% 38%; /* Original deep blue, could be updated to teal if needed */
    --sidebar-primary: 175 55% 45%; /* Teal-ish */
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 30 90% 60%; /* Orange */
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 230 40% 90%;
    --sidebar-ring: 175 55% 45%;
  }

  .dark {
    --background: 230 15% 10%; /* Dark Blue/Grey */
    --foreground: 228 67% 96%; /* Light Blue */
    --card: 230 15% 15%;
    --card-foreground: 228 67% 96%;
    --popover: 230 15% 15%;
    --popover-foreground: 228 67% 96%;

    --primary: 175 55% 60%; /* Lighter Teal */
    --primary-foreground: 0 0% 10%; /* Nearly Black for contrast */

    --secondary: 230 15% 20%;
    --secondary-foreground: 175 55% 60%; /* Lighter Teal for secondary text on dark secondary bg */

    --muted: 230 15% 20%;
    --muted-foreground: 230 30% 70%;

    --accent: 30 90% 65%; /* Lighter Orange */
    --accent-foreground: 0 0% 10%; /* Nearly Black for contrast */

    --destructive: 0 70% 50%;
    --destructive-foreground: 0 0% 98%;
    --border: 230 15% 25%;
    --input: 230 15% 20%;
    --ring: 175 55% 60%; /* Lighter Teal for ring */

    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    --sidebar-background: 230 15% 8%;
    --sidebar-foreground: 228 60% 90%;
    --sidebar-primary: 175 55% 60%; /* Lighter Teal */
    --sidebar-primary-foreground: 0 0% 10%;
    --sidebar-accent: 30 90% 70%; /* Lighter Orange */
    --sidebar-accent-foreground: 0 0% 10%;
    --sidebar-border: 230 15% 20%;
    --sidebar-ring: 175 55% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
