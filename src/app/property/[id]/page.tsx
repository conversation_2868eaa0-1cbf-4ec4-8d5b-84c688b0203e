import { getPropertyById, getProperties } from "@/data/mock-properties";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  BedDouble,
  Bath,
  MapPin,
  DollarSign,
  Star,
  CheckCircle,
  XCircle,
  Sparkles,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { notFound } from "next/navigation";
import MapSectionClient from "@/components/properties/MapSectionClient";
import PublicationNavRow from "@/components/properties/PublicationNavRow";
import PublicationImages from "@/components/properties/PublicationImages";
import WhatsAppContact from "@/components/ui/whatsapp-contact";

interface PropertyPageParams {
  id: string;
}

// The direct dynamic import of MapDisplay was removed from here.
// It is now handled by ClientMapLoader.tsx

export default async function PropertyPage({
  params,
}: {
  params: PropertyPageParams;
}) {
  const { id } = await params;
  const property = await getPropertyById(id);
  const allProperties = await getProperties();
  const propertyIds = allProperties.map((p) => p.id);
  const currentIndex = propertyIds.indexOf(id);
  const prevId =
    propertyIds[(currentIndex - 1 + propertyIds.length) % propertyIds.length];
  const nextId = propertyIds[(currentIndex + 1) % propertyIds.length];

  if (!property) {
    notFound();
  }

  return (
    <>
      {/* Sticky nav row for mobile */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <PublicationNavRow prevId={prevId} nextId={nextId} />
        <div className="grid grid-cols-1 py-4 lg:grid-cols-3 gap-8">
          {/* Left/Main: Facebook-style publication */}
          <div className="lg:col-span-2 space-y-8">
            {/* Property title and availability */}
            <div className="bg-card rounded-lg shadow p-4 space-y-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <h1 className="text-2xl font-bold text-foreground">
                  {property.name}
                </h1>
                <Badge
                  variant={property.available ? "default" : "destructive"}
                  className={`w-fit ${
                    property.available ? "bg-green-600 hover:bg-green-700" : ""
                  }`}
                >
                  {property.available ? "Disponible" : "No disponible"}
                </Badge>
              </div>
              <div className="mt-auto text-primary font-bold text-xl">
                ${property.pricePerMonth.toLocaleString("es-AR")}{" "}
                <span className="text-sm font-normal text-muted-foreground">
                  / mes
                </span>
              </div>
              <div className="flex items-center gap-2">
                {property.description}
              </div>
            </div>
            {/* Facebook-style image grid and modal */}
            <PublicationImages
              images={property.images}
              altText={property.name}
              property={property}
            />
            {/* Unified details card below images */}
            <div className="bg-card rounded-lg shadow p-4 space-y-6">
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 text-sm text-muted-foreground">
                {property.bedrooms > 0 && (
                  <div className="flex items-center">
                    <BedDouble className="h-5 w-5 mr-2 text-primary" />
                    {property.bedrooms} Dormitorios
                  </div>
                )}
                {property.bathrooms > 0 && (
                  <div className="flex items-center">
                    <Bath className="h-5 w-5 mr-2 text-primary" />
                    {property.bathrooms} Baños
                  </div>
                )}
                <div className="flex items-center">
                  <DollarSign className="h-5 w-5 mr-2 text-primary" />$
                  {property.pricePerMonth.toLocaleString("es-AR")} / mes
                </div>
              </div>
              {property.amenities.length > 0 && (
                <div>
                  <div className="font-semibold text-foreground mb-2">
                    Comodidades
                  </div>
                  <ul className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                    {property.amenities.map((amenity) => (
                      <li
                        key={amenity}
                        className="flex items-center gap-2 text-foreground"
                      >
                        <CheckCircle className="w-5 h-5 flex-shrink-0 text-primary" />
                        <span>{amenity}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              {property.features.length > 0 && (
                <div>
                  <div className="font-semibold text-foreground mb-2">
                    Características
                  </div>
                  <ul className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                    {property.features.map((feature) => (
                      <li
                        key={feature}
                        className="flex items-center gap-2 text-foreground"
                      >
                        <Sparkles className="w-5 h-5 flex-shrink-0 text-primary" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              {property.not_allowed.length > 0 && (
                <div>
                  <div className="font-semibold text-foreground mb-2">
                    Restricciones
                  </div>
                  <ul className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                    {property.not_allowed.map((item) => (
                      <li
                        key={item}
                        className="flex items-center gap-2 text-foreground"
                      >
                        <XCircle className="w-5 h-5 flex-shrink-0 text-destructive" />
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              {property.reviews.length > 0 && (
                <div>
                  <div className="font-semibold text-foreground mb-2">
                    User Reviews ({property.reviews.length})
                  </div>
                  <div className="space-y-6">
                    {property.reviews.map((review) => (
                      <div
                        key={review.id}
                        className="pb-4 border-b last:border-b-0"
                      >
                        <div className="flex items-center mb-1">
                          <p className="font-semibold text-foreground mr-2">
                            {review.author}
                          </p>
                          <div className="flex items-center">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`h-4 w-4 ${
                                  i < review.rating
                                    ? "fill-accent text-accent"
                                    : "text-muted-foreground/50"
                                }`}
                              />
                            ))}
                          </div>
                        </div>
                        <p className="text-xs text-muted-foreground mb-1">
                          {new Date(review.date).toLocaleDateString()}
                        </p>
                        <p className="text-foreground text-sm">
                          {review.comment}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Right: property info (host, map, nav row) */}
          <div className="lg:col-span-1 space-y-8 lg:sticky lg:top-24 self-start">
            <Card className="shadow-lg">
              <CardHeader>
                <CardTitle className="text-xl">
                  Información del anfitrión
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Avatar className="h-12 w-12">
                    <AvatarImage
                      src={property.host.avatar}
                      alt={property.host.name}
                      data-ai-hint="person portrait"
                    />
                    <AvatarFallback>
                      {property.host.name.substring(0, 2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-semibold text-foreground">
                      {property.host.name}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Superanfitrión
                    </p>
                  </div>
                </div>
                {property.host.whatsapp && (
                  <WhatsAppContact
                    phoneNumber={property.host.whatsapp}
                    message={`Hola, estoy interesado en la propiedad "${property.name}" ubicada en ${property.location.address}`}
                    className="w-full"
                    size="md"
                  >
                    Contactar por WhatsApp
                  </WhatsAppContact>
                )}
              </CardContent>
            </Card>

            <Card className="shadow-lg">
              <CardHeader>
                <CardTitle className="text-xl">Ubicación</CardTitle>
              </CardHeader>
              <CardContent>
                <span className="flex items-center pb-4">
                  <MapPin className="h-3 w-3 mr-1" />
                  {property.location.address}
                </span>
                <MapSectionClient
                  lat={property.location.lat}
                  lng={property.location.lng}
                  propertyName={property.name}
                  propertyId={property.id}
                />
              </CardContent>
            </Card>

            {/* Desktop nav row below map */}
            <PublicationNavRow prevId={prevId} nextId={nextId} desktop />
          </div>
        </div>
      </div>
    </>
  );
}
