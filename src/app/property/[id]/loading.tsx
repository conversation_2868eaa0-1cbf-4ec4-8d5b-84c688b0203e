import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import ImageSkeleton from "@/components/ui/image-skeleton";
import LoadingSpinner from "@/components/ui/loading-spinner";

export default function LoadingPropertyPage() {
  return (
    <div className="max-w-6xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column */}
        <div className="lg:col-span-2 space-y-8">
          {/* Property title and availability skeleton */}
          <div className="bg-card rounded-lg shadow p-4 space-y-4">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="h-8 bg-muted animate-pulse rounded w-3/4" />
              <div className="h-6 bg-muted animate-pulse rounded w-24" />
            </div>
            <div className="h-4 bg-muted animate-pulse rounded w-1/2" />
          </div>

          {/* Description skeleton */}
          <div className="bg-card rounded-lg shadow p-4 space-y-2">
            <div className="h-4 bg-muted animate-pulse rounded w-full" />
            <div className="h-4 bg-muted animate-pulse rounded w-full" />
            <div className="h-4 bg-muted animate-pulse rounded w-3/4" />
          </div>

          {/* Images skeleton */}
          <div className="bg-card rounded-lg shadow p-4">
            <ImageSkeleton aspectRatio="video" className="w-full h-96" />
          </div>

          {/* Details skeleton */}
          <div className="bg-card rounded-lg shadow p-4 space-y-6">
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 text-sm">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="flex items-center">
                  <div className="h-5 w-5 bg-muted animate-pulse rounded mr-2" />
                  <div className="h-4 bg-muted animate-pulse rounded w-20" />
                </div>
              ))}
            </div>

            {/* Amenities */}
            <div>
              <div className="h-5 bg-muted animate-pulse rounded w-24 mb-2" />
              <ul className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                {[...Array(6)].map((_, i) => (
                  <li key={i} className="flex items-center gap-2">
                    <div className="h-5 w-5 bg-muted animate-pulse rounded" />
                    <div className="h-4 bg-muted animate-pulse rounded w-24" />
                  </li>
                ))}
              </ul>
            </div>

            {/* Features */}
            <div>
              <div className="h-5 bg-muted animate-pulse rounded w-32 mb-2" />
              <ul className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                {[...Array(4)].map((_, i) => (
                  <li key={i} className="flex items-center gap-2">
                    <div className="h-5 w-5 bg-muted animate-pulse rounded" />
                    <div className="h-4 bg-muted animate-pulse rounded w-28" />
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="lg:col-span-1 space-y-8 lg:sticky lg:top-24 self-start">
          <Card className="shadow-lg">
            <CardHeader>
              <div className="h-6 bg-muted animate-pulse rounded w-1/2" />
            </CardHeader>
            <CardContent className="flex items-center space-x-3">
              <div className="h-12 w-12 bg-muted animate-pulse rounded-full" />
              <div>
                <div className="h-4 bg-muted animate-pulse rounded w-24 mb-1" />
                <div className="h-3 bg-muted animate-pulse rounded w-16" />
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-lg">
            <CardHeader>
              <div className="h-6 bg-muted animate-pulse rounded w-1/3" />
            </CardHeader>
            <CardContent>
              <div className="h-4 bg-muted animate-pulse rounded w-3/4 mb-4" />
              <ImageSkeleton aspectRatio="video" className="w-full h-48" />
            </CardContent>
          </Card>

          {/* Loading indicator */}
          <div className="flex justify-center">
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </div>
    </div>
  );
}
