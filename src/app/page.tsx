import { Suspense } from "react";
import { getProperties } from "@/data/mock-properties";
import PropertyListClient from "@/components/properties/PropertyListClient";
import PageLoader from "@/components/ui/page-loader";

async function PropertiesContent() {
  const properties = await getProperties();

  return (
    <section>
      <h2 className="text-3xl font-bold tracking-tight mb-2 text-center sm:text-left">
        Encuentra tu alquiler ideal en Goya
      </h2>
      <p className="text-muted-foreground mb-8 text-center sm:text-left">
        Explora una variedad de propiedades disponibles para alquilar en Goya,
        Corrientes.
      </p>
      <PropertyListClient initialProperties={properties} />
    </section>
  );
}

export default function HomePage() {
  return (
    <div className="space-y-8">
      <Suspense fallback={<PageLoader />}>
        <PropertiesContent />
      </Suspense>
    </div>
  );
}
