"use client";

import { useState, useRef, useCallback } from "react";

interface UseImageLoadingOptions {
  fallbackTimeout?: number;
}

export function useImageLoading(options: UseImageLoadingOptions = {}) {
  const { fallbackTimeout = 3000 } = options;
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(false);
  const [loadedImages, setLoadedImages] = useState<Record<string, boolean>>({});
  const timeoutRef = useRef<NodeJS.Timeout>();

  const startLoading = useCallback((imageKey?: string) => {
    setLoading(true);
    setError(false);
    
    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    // Set fallback timeout
    timeoutRef.current = setTimeout(() => {
      setLoading(false);
    }, fallbackTimeout);
  }, [fallbackTimeout]);

  const handleImageLoad = useCallback((imageKey?: string) => {
    setLoading(false);
    setError(false);
    
    if (imageKey) {
      setLoadedImages(prev => ({ ...prev, [imageKey]: true }));
    }
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  }, []);

  const handleImageError = useCallback((imageKey?: string) => {
    setLoading(false);
    setError(true);
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  }, []);

  const isImageLoaded = useCallback((imageKey: string) => {
    return loadedImages[imageKey] || false;
  }, [loadedImages]);

  const resetLoading = useCallback(() => {
    setLoading(false);
    setError(false);
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  }, []);

  return {
    loading,
    error,
    startLoading,
    handleImageLoad,
    handleImageError,
    isImageLoaded,
    resetLoading,
  };
}
