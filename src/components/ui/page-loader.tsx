"use client";

import LoadingSpinner from "./loading-spinner";
import ImageSkeleton from "./image-skeleton";

const PageLoader = () => {
  return (
    <div className="space-y-8">
      {/* Header skeleton */}
      <section>
        <div className="h-8 bg-muted animate-pulse rounded mb-2 w-3/4" />
        <div className="h-4 bg-muted animate-pulse rounded mb-8 w-1/2" />
        
        {/* Search and filters skeleton */}
        <div className="flex flex-col gap-4 mb-8">
          <div className="h-10 bg-muted animate-pulse rounded w-full" />
          <div className="flex flex-col sm:flex-row gap-4 sm:items-center">
            <div className="flex flex-col gap-2">
              <div className="h-4 bg-muted animate-pulse rounded w-12" />
              <div className="flex gap-2">
                <div className="h-8 bg-muted animate-pulse rounded w-16" />
                <div className="h-8 bg-muted animate-pulse rounded w-20" />
                <div className="h-8 bg-muted animate-pulse rounded w-16" />
              </div>
            </div>
            <div className="flex flex-col gap-2">
              <div className="h-4 bg-muted animate-pulse rounded w-20" />
              <div className="flex gap-2">
                <div className="h-8 bg-muted animate-pulse rounded w-16" />
                <div className="h-8 bg-muted animate-pulse rounded w-20" />
                <div className="h-8 bg-muted animate-pulse rounded w-24" />
              </div>
            </div>
          </div>
        </div>

        {/* Property cards skeleton */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="bg-card rounded-lg shadow border border-border overflow-hidden">
              <ImageSkeleton aspectRatio="video" className="w-full h-48" />
              <div className="p-4 space-y-3">
                <div className="flex items-center gap-2">
                  <div className="h-3 bg-muted animate-pulse rounded w-24" />
                  <div className="h-5 bg-muted animate-pulse rounded w-16" />
                </div>
                <div className="h-5 bg-muted animate-pulse rounded w-20" />
                <div className="h-4 bg-muted animate-pulse rounded w-full" />
                <div className="h-4 bg-muted animate-pulse rounded w-3/4" />
                <div className="h-6 bg-muted animate-pulse rounded w-32" />
              </div>
            </div>
          ))}
        </div>
      </section>
    </div>
  );
};

export default PageLoader;
