"use client";

import { MessageCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface WhatsAppContactProps {
  phoneNumber: string;
  message?: string;
  className?: string;
  variant?: "default" | "outline" | "ghost";
  size?: "sm" | "md" | "lg";
  children?: React.ReactNode;
}

export function WhatsAppContact({
  phoneNumber,
  message = "Hola, estoy interesado en esta propiedad",
  className,
  variant = "default",
  size = "md",
  children,
}: WhatsAppContactProps) {
  const handleWhatsAppClick = () => {
    // Clean phone number (remove spaces, dashes, etc.)
    const cleanPhone = phoneNumber.replace(/[^\d+]/g, "");
    
    // Encode the message for URL
    const encodedMessage = encodeURIComponent(message);
    
    // Create WhatsApp URL
    const whatsappUrl = `https://wa.me/${cleanPhone}?text=${encodedMessage}`;
    
    // Open WhatsApp in new tab
    window.open(whatsappUrl, "_blank", "noopener,noreferrer");
  };

  const sizeClasses = {
    sm: "h-8 px-3 text-xs",
    md: "h-10 px-4 py-2",
    lg: "h-11 px-8",
  };

  return (
    <Button
      onClick={handleWhatsAppClick}
      variant={variant}
      className={cn(
        "gap-2 bg-green-600 hover:bg-green-700 text-white",
        variant === "outline" && "border-green-600 text-green-600 bg-transparent hover:bg-green-50",
        variant === "ghost" && "bg-transparent text-green-600 hover:bg-green-50",
        sizeClasses[size],
        className
      )}
    >
      <MessageCircle className="h-4 w-4" />
      {children || "Contactar por WhatsApp"}
    </Button>
  );
}

export default WhatsAppContact;
