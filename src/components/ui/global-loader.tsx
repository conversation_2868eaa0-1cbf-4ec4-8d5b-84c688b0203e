"use client";

import { useEffect, useState } from "react";
import { usePathname } from "next/navigation";
import LoadingSpinner from "./loading-spinner";

const GlobalLoader = () => {
  const [isLoading, setIsLoading] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    setIsLoading(true);
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 100); // Short delay to show loader for very fast navigations

    return () => clearTimeout(timer);
  }, [pathname]);

  if (!isLoading) return null;

  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="bg-card p-6 rounded-lg shadow-lg border border-border">
        <LoadingSpinner size="lg" />
        <p className="mt-4 text-sm text-muted-foreground text-center">
          Cargando...
        </p>
      </div>
    </div>
  );
};

export default GlobalLoader;
