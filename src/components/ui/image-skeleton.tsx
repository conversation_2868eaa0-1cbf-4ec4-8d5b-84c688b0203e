"use client";

import { cn } from "@/lib/utils";

interface ImageSkeletonProps {
  className?: string;
  aspectRatio?: "square" | "video" | "auto";
}

const ImageSkeleton: React.FC<ImageSkeletonProps> = ({ 
  className, 
  aspectRatio = "auto" 
}) => {
  const aspectClasses = {
    square: "aspect-square",
    video: "aspect-video", 
    auto: ""
  };

  return (
    <div
      className={cn(
        "bg-muted animate-pulse rounded-lg flex items-center justify-center",
        aspectClasses[aspectRatio],
        className
      )}
    >
      <div className="w-8 h-8 bg-muted-foreground/20 rounded animate-pulse" />
    </div>
  );
};

export default ImageSkeleton;
