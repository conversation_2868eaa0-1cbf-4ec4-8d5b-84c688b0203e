"use client";

import { useEffect } from "react";
import { cn } from "@/lib/utils";
import ImageSkeleton from "./image-skeleton";
import { useImageLoading } from "@/hooks/use-image-loading";

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  fill?: boolean;
  className?: string;
  priority?: boolean;
  aspectRatio?: "square" | "video" | "auto";
}

const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  fill = false,
  className,
  priority = false,
  aspectRatio = "auto",
}) => {
  const {
    loading,
    error,
    startLoading,
    handleImageLoad,
    handleImageError,
    isImageLoaded,
  } = useImageLoading();

  useEffect(() => {
    if (!isImageLoaded(src)) {
      startLoading(src);
    }
  }, [src, isImageLoaded, startLoading]);

  if (error) {
    return (
      <div
        className={cn(
          "bg-muted flex items-center justify-center text-muted-foreground text-sm",
          aspectRatio === "square" && "aspect-square",
          aspectRatio === "video" && "aspect-video",
          className
        )}
      >
        Failed to load image
      </div>
    );
  }

  return (
    <div className="relative w-full h-full">
      {loading && (
        <ImageSkeleton
          className="absolute inset-0 z-10"
          aspectRatio={aspectRatio}
        />
      )}
      <img
        src={src}
        alt={alt}
        className={cn(
          "transition-opacity duration-300",
          fill ? "absolute inset-0 w-full h-full object-cover" : "",
          loading ? "opacity-0" : "opacity-100",
          className
        )}
        style={{
          width: fill ? "100%" : width,
          height: fill ? "100%" : height,
        }}
        onLoad={() => handleImageLoad(src)}
        onError={() => handleImageError(src)}
        loading={priority ? "eager" : "lazy"}
      />
    </div>
  );
};

export default OptimizedImage;
