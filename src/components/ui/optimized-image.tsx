"use client";

import { useEffect } from "react";
import Image from "next/image";
import { cn } from "@/lib/utils";
import ImageSkeleton from "./image-skeleton";
import { useImageLoading } from "@/hooks/use-image-loading";

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  fill?: boolean;
  className?: string;
  priority?: boolean;
  aspectRatio?: "square" | "video" | "auto";
  sizes?: string;
  quality?: number;
}

const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  fill = false,
  className,
  priority = false,
  aspectRatio = "auto",
  sizes,
  quality = 75,
}) => {
  const {
    loading,
    error,
    startLoading,
    handleImageLoad,
    handleImageError,
    isImageLoaded,
  } = useImageLoading();

  useEffect(() => {
    if (!isImageLoaded(src)) {
      startLoading(src);
    }
  }, [src, isImageLoaded, startLoading]);

  if (error) {
    return (
      <div
        className={cn(
          "bg-muted flex items-center justify-center text-muted-foreground text-sm",
          aspectRatio === "square" && "aspect-square",
          aspectRatio === "video" && "aspect-video",
          className
        )}
      >
        Failed to load image
      </div>
    );
  }

  return (
    <div className="relative w-full h-full">
      {loading && (
        <ImageSkeleton
          className="absolute inset-0 z-10"
          aspectRatio={aspectRatio}
        />
      )}
      <Image
        src={src}
        alt={alt}
        width={width}
        height={height}
        fill={fill}
        priority={priority}
        quality={quality}
        sizes={sizes}
        className={cn(
          "transition-opacity duration-300",
          loading ? "opacity-0" : "opacity-100",
          className
        )}
        onLoad={() => handleImageLoad(src)}
        onError={() => handleImageError(src)}
        loading={priority ? "eager" : "lazy"}
      />
    </div>
  );
};

export default OptimizedImage;
