
'use client';

import { useEffect, useState } from 'react';

const ThemeProvider = ({ children }: { children: React.ReactNode }) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    // This effect runs once on mount to signal client-side readiness.
    setMounted(true);
  }, []);

  useEffect(() => {
    // This effect runs only on the client, after the component has mounted.
    if (!mounted) return;

    const currentHour = new Date().getHours();
    // Dark mode between 7 PM (19:00) and 5:59 AM (exclusive of 6:00)
    const isNightTime = currentHour >= 19 || currentHour < 6;

    if (isNightTime) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }

    // Optional: Could also listen for system preference changes
    // const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    // const handleChange = () => { ... apply theme ... };
    // mediaQuery.addEventListener('change', handleChange);
    // return () => mediaQuery.removeEventListener('change', handleChange);
  }, [mounted]); // Re-run when mounted state changes (effectively once after mount)

  // To prevent hydration mismatch, we ensure the component logic that modifies
  // document.documentElement only runs after `mounted` is true.
  // The `suppressHydrationWarning` on the `<html>` tag in RootLayout
  // also helps manage potential differences between server and client initial render.
  return <>{children}</>;
};

export default ThemeProvider;
