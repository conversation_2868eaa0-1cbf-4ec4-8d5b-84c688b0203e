import Link from "next/link";
import ThemeToggle from "./ThemeToggle";

const Header = () => {
  return (
    <header className="bg-card border-b border-border shadow-sm sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4 flex justify-between items-center">
        <Link
          href="/"
          className="flex items-center gap-2 text-primary hover:text-primary/80 transition-colors"
        >
          <img
            src="/ag.svg"
            alt="Alquileres Goya"
            width={240}
            height={120}
            className="h-20 w-auto dark:invert"
          />
        </Link>
        <ThemeToggle />
      </div>
    </header>
  );
};

export default Header;
