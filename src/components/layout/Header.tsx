import Link from "next/link";
import { Building } from "lucide-react";
import ThemeToggle from "./ThemeToggle";

const Header = () => {
  return (
    <header className="bg-card border-b border-border shadow-sm sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4 flex justify-between items-center">
        <Link
          href="/"
          className="flex items-center gap-2 text-primary hover:text-primary/80 transition-colors"
        >
          <Building className="h-8 w-8" />
          <h1 className="text-2xl font-bold">Alquileres Goya</h1>
        </Link>
        <ThemeToggle />
      </div>
    </header>
  );
};

export default Header;
