"use client";

import type { PropertyType } from "@/types";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface TypeFilterProps {
  propertyTypes: PropertyType[];
  selectedType: string | null;
  onTypeChange: (type: string | null) => void;
}

const TypeFilter: React.FC<TypeFilterProps> = ({
  propertyTypes,
  selectedType,
  onTypeChange,
}) => {
  return (
    // Removed mb-8 from this div. The parent component handles overall margin.
    <div>
      <Select
        value={selectedType || "all"}
        onValueChange={(value) =>
          onTypeChange(value === "all" ? null : (value as PropertyType))
        }
      >
        <SelectTrigger className="w-full sm:w-[280px] bg-card shadow-sm">
          <SelectValue placeholder="Filtrar por tipo de propiedad" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">Todos los tipos</SelectItem>
          {propertyTypes.map((type) => (
            <SelectItem key={type} value={type}>
              {type}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default TypeFilter;
