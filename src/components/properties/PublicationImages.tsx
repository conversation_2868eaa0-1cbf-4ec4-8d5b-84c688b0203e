"use client";
import { useState } from "react";
import OptimizedImage from "@/components/ui/optimized-image";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { useIsMobile } from "@/hooks/use-mobile";
import type { Property } from "@/types";
import { motion, AnimatePresence } from "framer-motion";
import {
  X,
  ChevronLeft,
  ChevronRight,
  CheckCircle,
  Sparkles,
  Star,
  XCircle,
  MapPin,
} from "lucide-react";

interface PublicationImagesProps {
  images: string[];
  altText: string;
  property: Property;
}

const srOnly = "sr-only";

const PublicationImages: React.FC<PublicationImagesProps> = ({
  images,
  altText,
  property,
}) => {
  const [modalIndex, setModalIndex] = useState<number | null>(null);
  const [swipeDirection, setSwipeDirection] = useState<"left" | "right" | null>(
    null
  );
  const isMobile = useIsMobile();

  const goPrev = (e?: React.MouseEvent) => {
    if (e) e.stopPropagation();
    if (modalIndex !== null) {
      setSwipeDirection("right");
      setModalIndex((modalIndex - 1 + images.length) % images.length);
    }
  };
  const goNext = (e?: React.MouseEvent) => {
    if (e) e.stopPropagation();
    if (modalIndex !== null) {
      setSwipeDirection("left");
      setModalIndex((modalIndex + 1) % images.length);
    }
  };
  const closeModal = (e?: React.MouseEvent) => {
    if (e) e.stopPropagation();
    setModalIndex(null);
  };

  // Desktop: Facebook-style grid/collage
  if (!isMobile) {
    const gridImages = images.slice(0, 4);
    const extraCount = images.length - 4;
    return (
      <>
        <div className="bg-card rounded-lg shadow p-4 space-y-6">
          <div
            className={`grid gap-1 w-full aspect-video ${
              gridImages.length === 1
                ? ""
                : gridImages.length === 2
                ? "grid-cols-2"
                : gridImages.length === 3
                ? "grid-cols-2 grid-rows-2"
                : "grid-cols-2 grid-rows-2"
            }`}
            style={{ height: "auto", width: "100%" }}
          >
            {gridImages.map((img, i) => (
              <button
                key={img}
                className={`relative ${
                  gridImages.length === 3 && i === 2
                    ? "col-span-2 row-start-2"
                    : ""
                } w-full h-full group`}
                style={{ minHeight: 0, minWidth: 0 }}
                onClick={() => setModalIndex(i)}
              >
                <OptimizedImage
                  src={img}
                  alt={altText}
                  fill
                  className="object-cover group-hover:brightness-90 transition"
                  quality={70}
                  sizes="(max-width: 768px) 100vw, 50vw"
                />
                {extraCount > 0 && i === 3 && (
                  <div className="absolute inset-0 bg-black/60 flex items-center justify-center text-white text-2xl font-bold">
                    +{extraCount}
                  </div>
                )}
              </button>
            ))}
          </div>
          <AnimatePresence>
            {modalIndex !== null && (
              <Dialog
                open
                onOpenChange={(open) => {
                  if (!open) setModalIndex(null);
                }}
              >
                <DialogContent className="!fixed !inset-0 !w-screen !h-screen !flex !flex-row !p-0 !bg-transparent !shadow-2xl !items-stretch !justify-center !z-50 !translate-x-0 !translate-y-0 !max-w-none !max-h-none !border-0 !rounded-none">
                  <DialogTitle className={srOnly}>{property.name}</DialogTitle>
                  {/* Image section */}
                  <div className="relative w-[60vw] max-w-[60vw] min-w-[300px] h-full flex items-center justify-center bg-black">
                    {/* Left arrow */}
                    {images.length > 1 && (
                      <button
                        onClick={goPrev}
                        aria-label="Anterior"
                        className="absolute left-2 top-1/2 -translate-y-1/2 z-30 bg-black/60 hover:bg-black/80 rounded-full p-2 text-white"
                      >
                        <ChevronLeft className="h-8 w-8" />
                      </button>
                    )}
                    {/* Image */}
                    {images[modalIndex] ? (
                      <OptimizedImage
                        src={images[modalIndex]}
                        alt={altText}
                        fill
                        className="object-contain"
                        priority
                        quality={85}
                        sizes="60vw"
                      />
                    ) : (
                      <div className="flex items-center justify-center w-full h-full bg-muted text-muted-foreground">
                        No Image Available
                      </div>
                    )}
                    {/* Right arrow */}
                    {images.length > 1 && (
                      <button
                        onClick={goNext}
                        aria-label="Siguiente"
                        className="absolute right-2 top-1/2 -translate-y-1/2 z-30 bg-black/60 hover:bg-black/80 rounded-full p-2 text-white"
                      >
                        <ChevronRight className="h-8 w-8" />
                      </button>
                    )}
                  </div>
                  {/* Info panel: todos los detalles de la publicación */}
                  <div className="h-full max-w-xs min-w-[250px] w-full bg-card p-4 overflow-y-auto flex flex-col flex-shrink-0 min-h-0">
                    <div className="font-bold text-2xl mb-2 text-foreground">
                      {property.name}
                    </div>
                    <div className="text-muted-foreground text-sm mb-2 flex items-center gap-2">
                      <MapPin className="h-3 w-3 mr-1" />
                      <span>{property.location.address}</span>
                    </div>
                    <div className="text-primary text-xl font-bold mb-4">
                      ${property.pricePerMonth.toLocaleString("es-AR")}{" "}
                      <span className="text-sm font-normal text-muted-foreground">
                        / mes
                      </span>
                    </div>
                    <div className="mb-4 text-foreground whitespace-pre-line">
                      {property.description}
                    </div>
                    {property.amenities.length > 0 && (
                      <div className="mb-4">
                        <div className="font-semibold mb-1">Comodidades</div>
                        <ul className="grid grid-cols-1 gap-2">
                          {property.amenities.map((amenity) => (
                            <li
                              key={amenity}
                              className="flex items-center gap-2 text-foreground"
                            >
                              <CheckCircle className="w-5 h-5 flex-shrink-0 text-primary" />
                              <span>{amenity}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                    {property.features.length > 0 && (
                      <div className="mb-4">
                        <div className="font-semibold mb-1">
                          Características
                        </div>
                        <ul className="grid grid-cols-1 gap-2">
                          {property.features.map((feature) => (
                            <li
                              key={feature}
                              className="flex items-center gap-2 text-foreground"
                            >
                              <Sparkles className="w-5 h-5 flex-shrink-0 text-primary" />
                              <span>{feature}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                    {property.not_allowed.length > 0 && (
                      <div className="mb-4">
                        <div className="font-semibold mb-1">Restricciones</div>
                        <ul className="grid grid-cols-1 gap-2">
                          {property.not_allowed.map((item) => (
                            <li
                              key={item}
                              className="flex items-center gap-2 text-foreground"
                            >
                              <XCircle className="w-5 h-5 flex-shrink-0 text-destructive" />
                              <span>{item}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                    {property.reviews.length > 0 && (
                      <div className="mb-4">
                        <div className="font-semibold mb-1">
                          User Reviews ({property.reviews.length})
                        </div>
                        <div className="space-y-4">
                          {property.reviews.map((review) => (
                            <div
                              key={review.id}
                              className="border-b pb-2 last:border-b-0"
                            >
                              <div className="flex items-center mb-1">
                                <span className="font-semibold mr-2">
                                  {review.author}
                                </span>
                                <span className="flex items-center">
                                  {[...Array(5)].map((_, i) => (
                                    <Star
                                      key={i}
                                      className={`h-4 w-4 ${
                                        i < review.rating
                                          ? "fill-accent text-accent"
                                          : "text-muted-foreground/50"
                                      }`}
                                    />
                                  ))}
                                </span>
                              </div>
                              <div className="text-xs text-muted-foreground mb-1">
                                {new Date(review.date).toLocaleDateString()}
                              </div>
                              <div className="text-foreground text-sm">
                                {review.comment}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                  {/* Close button */}
                  <button
                    onClick={closeModal}
                    aria-label="Cerrar"
                    className="absolute top-4 right-4 z-40 bg-black/60 hover:bg-black/80 rounded-full p-2 text-white"
                  >
                    <X className="h-6 w-6" />
                  </button>
                </DialogContent>
              </Dialog>
            )}
          </AnimatePresence>
        </div>
      </>
    );
  }

  // Mobile: Facebook-style swipeable modal
  return (
    <>
      <div className="flex flex-col gap-2">
        {images.map((img, i) => (
          <button
            key={img}
            className="relative w-full aspect-video rounded-lg overflow-hidden"
            onClick={() => setModalIndex(i)}
          >
            <OptimizedImage
              src={img}
              alt={altText}
              fill
              className="object-cover"
              aspectRatio="video"
              quality={60}
              sizes="100vw"
            />
          </button>
        ))}
      </div>
      <AnimatePresence>
        {modalIndex !== null && (
          <Dialog
            open
            onOpenChange={(open) => {
              if (!open) setModalIndex(null);
            }}
          >
            <DialogContent className="fixed inset-0 w-screen h-screen flex flex-col p-0 bg-black z-50 overflow-hidden">
              <DialogTitle className={srOnly}>{property.name}</DialogTitle>
              {/* Overlay superior: cerrar y contador */}
              <div className="absolute top-0 left-0 w-full flex items-center justify-between px-4 py-3 z-30 bg-gradient-to-b from-black/70 to-transparent">
                <button
                  onClick={closeModal}
                  aria-label="Cerrar"
                  className="text-white text-3xl"
                >
                  <X className="h-8 w-8" />
                </button>
                <div className="text-white text-lg font-semibold drop-shadow">
                  {modalIndex + 1} de {images.length}
                </div>
                <div className="w-8" /> {/* Espaciador para simetría */}
              </div>
              {/* Imagen swipeable */}
              <div className="relative w-full h-[70vh] max-h-[70vh] flex items-center justify-center bg-black overflow-hidden">
                <motion.div
                  key={modalIndex}
                  className="w-full h-full flex items-center justify-center"
                  initial={{
                    x:
                      swipeDirection === "left"
                        ? 300
                        : swipeDirection === "right"
                        ? -300
                        : 0,
                    opacity: swipeDirection ? 0.8 : 1,
                  }}
                  animate={{ x: 0, opacity: 1 }}
                  exit={{
                    x:
                      swipeDirection === "left"
                        ? -300
                        : swipeDirection === "right"
                        ? 300
                        : 0,
                    opacity: 0.8,
                  }}
                  transition={{
                    type: "tween",
                    duration: 0.25,
                    ease: "easeOut",
                  }}
                  drag="x"
                  dragConstraints={{ left: -100, right: 100 }}
                  dragElastic={0.1}
                  onDragEnd={(_e, { offset, velocity }) => {
                    const swipeThreshold = 50;
                    const velocityThreshold = 500;

                    // Check if swipe was strong enough (distance or velocity)
                    const shouldSwipe =
                      Math.abs(offset.x) > swipeThreshold ||
                      Math.abs(velocity.x) > velocityThreshold;

                    if (shouldSwipe) {
                      if (offset.x > 0) {
                        // Swiped right, go to previous image (cycles to last if at first)
                        setSwipeDirection("right");
                        setModalIndex(
                          (modalIndex - 1 + images.length) % images.length
                        );
                      } else if (offset.x < 0) {
                        // Swiped left, go to next image (cycles to first if at last)
                        setSwipeDirection("left");
                        setModalIndex((modalIndex + 1) % images.length);
                      }
                    }
                  }}
                  onAnimationComplete={() => {
                    // Reset swipe direction after animation completes
                    setSwipeDirection(null);
                  }}
                  style={{ touchAction: "pan-y" }}
                >
                  <div className="relative w-full h-full flex items-center justify-center">
                    <OptimizedImage
                      src={images[modalIndex]}
                      alt={altText}
                      fill
                      className="object-contain"
                      priority
                      quality={85}
                      sizes="100vw"
                    />
                  </div>
                </motion.div>
              </div>
              {/* Panel de detalles debajo de la imagen */}
              <div className="w-full h-[30vh] max-h-[30vh] bg-card p-4 overflow-y-auto flex flex-col flex-shrink-0">
                <div className="font-bold text-lg mb-2 text-foreground">
                  {property.name}
                </div>
                <div className="text-muted-foreground text-sm mb-2 flex items-center gap-2">
                  <span>{property.location.address}</span>
                </div>
                <div className="text-primary text-xl font-bold mb-2">
                  ${property.pricePerMonth.toLocaleString("es-AR")}{" "}
                  <span className="text-sm font-normal text-muted-foreground">
                    / mes
                  </span>
                </div>
                <div className="mb-2 text-foreground whitespace-pre-line">
                  {property.description}
                </div>
                {property.amenities.length > 0 && (
                  <div className="mb-2">
                    <div className="font-semibold mb-1">Comodidades</div>
                    <ul className="grid grid-cols-1 gap-2">
                      {property.amenities.map((amenity) => (
                        <li
                          key={amenity}
                          className="flex items-center gap-2 text-foreground"
                        >
                          <CheckCircle className="w-5 h-5 flex-shrink-0 text-primary" />
                          <span>{amenity}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                {property.features.length > 0 && (
                  <div className="mb-2">
                    <div className="font-semibold mb-1">Características</div>
                    <ul className="grid grid-cols-1 gap-2">
                      {property.features.map((feature) => (
                        <li
                          key={feature}
                          className="flex items-center gap-2 text-foreground"
                        >
                          <Sparkles className="w-5 h-5 flex-shrink-0 text-primary" />
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                {property.not_allowed.length > 0 && (
                  <div className="mb-2">
                    <div className="font-semibold mb-1">Restricciones</div>
                    <ul className="grid grid-cols-1 gap-2">
                      {property.not_allowed.map((item) => (
                        <li
                          key={item}
                          className="flex items-center gap-2 text-foreground"
                        >
                          <XCircle className="w-5 h-5 flex-shrink-0 text-destructive" />
                          <span>{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                {property.reviews.length > 0 && (
                  <div className="mb-2">
                    <div className="font-semibold mb-1">
                      User Reviews ({property.reviews.length})
                    </div>
                    <div className="space-y-2">
                      {property.reviews.map((review) => (
                        <div
                          key={review.id}
                          className="border-b pb-2 last:border-b-0"
                        >
                          <div className="flex items-center mb-1">
                            <span className="font-semibold mr-2">
                              {review.author}
                            </span>
                            <span className="flex items-center">
                              {[...Array(5)].map((_, i) => (
                                <Star
                                  key={i}
                                  className={`h-4 w-4 ${
                                    i < review.rating
                                      ? "fill-accent text-accent"
                                      : "text-muted-foreground/50"
                                  }`}
                                />
                              ))}
                            </span>
                          </div>
                          <div className="text-xs text-muted-foreground mb-1">
                            {new Date(review.date).toLocaleDateString()}
                          </div>
                          <div className="text-foreground text-sm">
                            {review.comment}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </DialogContent>
          </Dialog>
        )}
      </AnimatePresence>
    </>
  );
};

export default PublicationImages;
