"use client";

import { useState, useMemo } from "react";
import type { Property, PropertyType } from "@/types";
import PropertyCard from "./PropertyCard";
import TypeFilter from "./TypeFilter";
import AvailabilityFilter from "./AvailabilityFilter";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";

interface PropertyListClientProps {
  initialProperties: Property[];
}

const PropertyListClient: React.FC<PropertyListClientProps> = ({
  initialProperties,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedType, setSelectedType] = useState<PropertyType | null>(null);
  const [selectedAvailability, setSelectedAvailability] = useState<
    boolean | null
  >(null);

  const propertyTypes = useMemo(() => {
    const types = new Set(initialProperties.map((p) => p.type));
    return Array.from(types) as PropertyType[];
  }, [initialProperties]);

  const filteredProperties = useMemo(() => {
    return initialProperties.filter((property) => {
      const matchesType = selectedType ? property.type === selectedType : true;
      const matchesAvailability =
        selectedAvailability !== null
          ? property.available === selectedAvailability
          : true;
      const matchesSearch =
        property.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        property.location.address
          .toLowerCase()
          .includes(searchTerm.toLowerCase());
      return matchesType && matchesAvailability && matchesSearch;
    });
  }, [initialProperties, selectedType, selectedAvailability, searchTerm]);

  return (
    <div>
      {/* Changed sm:items-center to sm:items-baseline for potentially better text alignment */}
      <div className="flex flex-col gap-4 mb-8">
        <div className="relative w-full">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
          <Input
            type="text"
            placeholder="Busca por nombre o localidad..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 w-full bg-card shadow-sm"
          />
        </div>
        <div className="flex flex-col sm:flex-row gap-4 sm:items-center">
          <div className="flex flex-col gap-2">
            <span className="text-sm font-medium text-foreground">Tipo:</span>
            <TypeFilter
              propertyTypes={propertyTypes}
              selectedType={selectedType}
              onTypeChange={setSelectedType}
            />
          </div>
          <div className="flex flex-col gap-2">
            <span className="text-sm font-medium text-foreground">
              Disponibilidad:
            </span>
            <AvailabilityFilter
              selectedAvailability={selectedAvailability}
              onAvailabilityChange={setSelectedAvailability}
            />
          </div>
        </div>
      </div>

      {filteredProperties.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredProperties.map((property) => (
            <PropertyCard key={property.id} property={property} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-xl text-muted-foreground">
            No properties found matching your criteria.
          </p>
        </div>
      )}
    </div>
  );
};

export default PropertyListClient;
