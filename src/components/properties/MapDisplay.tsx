"use client";

import { Map, Marker } from "pigeon-maps";

interface MapDisplayProps {
  lat: number;
  lng: number;
  propertyName: string;
  propertyId?: string | number;
}

const MapDisplay: React.FC<MapDisplayProps> = ({ lat, lng, propertyName }) => {
  const isMobile = () => {
    if (typeof navigator === "undefined") return false;
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    );
  };

  const handleMarkerClick = () => {
    const destination = `${lat},${lng}`;
    const isIOS = () =>
      typeof navigator !== "undefined" &&
      /iPad|iPhone|iPod/.test(navigator.userAgent);

    if (isIOS()) {
      // Open in Apple Maps
      window.open(`maps://maps.apple.com/?daddr=${destination}`, "_blank");
    } else if (
      /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent
      )
    ) {
      // Use universal maps URL for other mobile
      window.open(
        `https://maps.google.com/maps?daddr=${destination}`,
        "_blank"
      );
    } else {
      // Desktop: open Google Maps directions in new tab
      window.open(
        `https://www.google.com/maps/dir/?api=1&destination=${destination}`,
        "_blank"
      );
    }
  };

  return (
    <div className="aspect-video w-full rounded-lg overflow-hidden shadow-md">
      <Map height={300} defaultCenter={[lat, lng]} defaultZoom={14}>
        <Marker width={50} anchor={[lat, lng]} onClick={handleMarkerClick} />
      </Map>
    </div>
  );
};

export default MapDisplay;
