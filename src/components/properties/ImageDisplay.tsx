"use client";

import { useState, useRef, useEffect } from "react";
import Image from "next/image";
import { ChevronLeft, ChevronRight, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogTrigger,
  DialogTitle,
} from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import {
  motion,
  AnimatePresence,
  useMotionValue,
  useAnimation,
} from "framer-motion";
import { useIsMobile } from "@/hooks/use-mobile";

interface ImageDisplayProps {
  images: string[];
  altText: string;
}

const ImageDisplay: React.FC<ImageDisplayProps> = ({ images, altText }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loadingModal, setLoadingModal] = useState(false);
  const [direction, setDirection] = useState(0);
  const [imageLoaded, setImageLoaded] = useState<Record<number, boolean>>({});

  const isMobile = useIsMobile();

  // For continuous swipe
  const xCarousel = useMotionValue(0);
  const controlsCarousel = useAnimation();
  const xModal = useMotionValue(0);
  const controlsModal = useAnimation();
  const dragThreshold = 120; // px
  const prevIndexRef = useRef(currentIndex);
  const loadingTimeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    // Reset x to 0 when image changes
    controlsCarousel.start({
      x: 0,
      transition: { type: "spring", stiffness: 300, damping: 30 },
    });
    controlsModal.start({
      x: 0,
      transition: { type: "spring", stiffness: 300, damping: 30 },
    });

    // Clear any existing timeout
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
    }

    // If image is not loaded, show loading state
    if (!imageLoaded[currentIndex]) {
      setLoading(true);
      setLoadingModal(true);

      // Fallback timeout to hide loading state
      loadingTimeoutRef.current = setTimeout(() => {
        setLoading(false);
        setLoadingModal(false);
      }, 3000); // 3 second timeout
    } else {
      setLoading(false);
      setLoadingModal(false);
    }

    return () => {
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    };
    // eslint-disable-next-line
  }, [currentIndex]);

  if (!images || images.length === 0) {
    return (
      <div className="aspect-video w-full bg-muted rounded-lg flex items-center justify-center text-muted-foreground">
        No Image Available
      </div>
    );
  }

  const handleImageLoad = (index: number) => {
    setImageLoaded((prev) => ({ ...prev, [index]: true }));
    if (index === currentIndex) {
      setLoading(false);
      setLoadingModal(false);
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    }
  };

  const paginate = (newDirection: number) => {
    if (loading) return; // Prevent overlapping animations
    setDirection(newDirection);
    const newIndex =
      newDirection === 1
        ? currentIndex === images.length - 1
          ? 0
          : currentIndex + 1
        : currentIndex === 0
        ? images.length - 1
        : currentIndex - 1;

    // Show loading immediately if image not loaded
    if (!imageLoaded[newIndex]) {
      setLoading(true);
      setLoadingModal(true);
    }

    setCurrentIndex(newIndex);
  };

  const goToIndex = (index: number) => {
    if (loading || index === currentIndex) return;
    setDirection(index > currentIndex ? 1 : -1);

    // Show loading immediately if image not loaded
    if (!imageLoaded[index]) {
      setLoading(true);
      setLoadingModal(true);
    }

    setCurrentIndex(index);
  };

  const openImageDialog = () => {
    setIsDialogOpen(true);
  };

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <div className="relative w-full aspect-video group">
        {" "}
        {/* Main container for positioning */}
        <DialogTrigger asChild>
          <button
            type="button"
            className="absolute inset-0 w-full h-full z-10 cursor-pointer overflow-hidden rounded-lg shadow-lg group focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            onClick={openImageDialog}
            aria-label={`View image ${currentIndex + 1} larger`}
          >
            <div className="relative w-full h-full">
              <AnimatePresence initial={false} custom={direction}>
                <motion.div
                  key={images[currentIndex] + "-carousel"}
                  className="absolute inset-0 w-full h-full"
                  custom={direction}
                  style={{ x: xCarousel, touchAction: "pan-y" }}
                  drag="x"
                  dragConstraints={{ left: 0, right: 0 }}
                  onDragEnd={async (_, { offset }) => {
                    if (offset.x < -dragThreshold) {
                      await controlsCarousel.start({
                        x: -500,
                        opacity: 0,
                        transition: { duration: 0.2 },
                      });
                      paginate(1);
                      xCarousel.set(0);
                    } else if (offset.x > dragThreshold) {
                      await controlsCarousel.start({
                        x: 500,
                        opacity: 0,
                        transition: { duration: 0.2 },
                      });
                      paginate(-1);
                      xCarousel.set(0);
                    } else {
                      controlsCarousel.start({
                        x: 0,
                        transition: {
                          type: "spring",
                          stiffness: 300,
                          damping: 30,
                        },
                      });
                    }
                  }}
                  animate={controlsCarousel}
                  initial={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                >
                  {loading && (
                    <div className="absolute inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm z-30 rounded-lg">
                      <div className="flex flex-col items-center gap-2">
                        <Loader2 className="h-8 w-8 animate-spin text-primary" />
                      </div>
                    </div>
                  )}
                  <Image
                    src={images[currentIndex]}
                    alt={`${altText} - Image ${currentIndex + 1}`}
                    fill
                    className={cn(
                      "object-cover transition-all duration-500 ease-in-out group-hover:scale-105",
                      loading ? "opacity-0" : "opacity-100"
                    )}
                    data-ai-hint="property interior"
                    priority={currentIndex === 0}
                    onLoad={() => handleImageLoad(currentIndex)}
                    onError={() => {
                      setLoading(false);
                      if (loadingTimeoutRef.current) {
                        clearTimeout(loadingTimeoutRef.current);
                      }
                    }}
                  />
                </motion.div>
              </AnimatePresence>
            </div>
          </button>
        </DialogTrigger>
        {images.length > 1 && !isMobile && (
          <>
            <Button
              variant="ghost"
              size="icon"
              className="absolute left-2 top-1/2 -translate-y-1/2 bg-background/50 hover:bg-background/80 text-foreground rounded-full opacity-0 group-hover:opacity-100 transition-opacity z-20"
              onClick={(e) => {
                e.stopPropagation();
                paginate(-1);
              }}
              aria-label="Previous Image"
            >
              <ChevronLeft className="h-6 w-6" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-2 top-1/2 -translate-y-1/2 bg-background/50 hover:bg-background/80 text-foreground rounded-full opacity-0 group-hover:opacity-100 transition-opacity z-20"
              onClick={(e) => {
                e.stopPropagation();
                paginate(1);
              }}
              aria-label="Next Image"
            >
              <ChevronRight className="h-6 w-6" />
            </Button>
            <div className="absolute bottom-2 left-1/2 -translate-x-1/2 flex space-x-1.5 opacity-0 group-hover:opacity-100 transition-opacity z-20">
              {images.map((_, index) => (
                <button
                  key={index}
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation();
                    goToIndex(index);
                  }}
                  aria-label={`Go to image ${index + 1}`}
                  className={cn(
                    "h-2 w-2 rounded-full transition-colors",
                    currentIndex === index
                      ? "bg-primary"
                      : "bg-muted-foreground/50 hover:bg-muted-foreground"
                  )}
                />
              ))}
            </div>
          </>
        )}
      </div>

      <DialogContent
        className="p-2 bg-card rounded-lg flex flex-col items-center justify-center shadow-2xl"
        style={{
          width: "90vw",
          maxWidth: "90vw",
          height: "90vh",
          maxHeight: "90vh",
        }}
      >
        <DialogTitle className="mb-2 text-center w-full">{altText}</DialogTitle>
        <div className="relative w-full h-full flex items-center justify-center">
          {!isMobile && images.length > 1 && (
            <button
              type="button"
              className="absolute left-2 top-1/2 -translate-y-1/2 z-20 bg-background/70 hover:bg-background/90 rounded-full p-2 shadow"
              onClick={() => paginate(-1)}
              aria-label="Previous Image"
              style={{ display: images.length > 1 ? "block" : "none" }}
            >
              <ChevronLeft className="h-8 w-8" />
            </button>
          )}
          <div className="relative w-full h-full flex items-center justify-center">
            {loadingModal && (
              <div className="absolute inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm z-30 rounded-lg">
                <div className="flex flex-col items-center gap-2">
                  <Loader2 className="h-12 w-12 animate-spin text-primary" />
                </div>
              </div>
            )}
            <AnimatePresence initial={false} custom={direction}>
              <motion.div
                key={images[currentIndex] + "-modal"}
                className="absolute inset-0 w-full h-full flex items-center justify-center"
                custom={direction}
                style={{ x: xModal, touchAction: "pan-y" }}
                drag="x"
                dragConstraints={{ left: 0, right: 0 }}
                onDragEnd={async (_, { offset }) => {
                  if (offset.x < -dragThreshold) {
                    await controlsModal.start({
                      x: -500,
                      opacity: 0,
                      transition: { duration: 0.2 },
                    });
                    paginate(1);
                    xModal.set(0);
                  } else if (offset.x > dragThreshold) {
                    await controlsModal.start({
                      x: 500,
                      opacity: 0,
                      transition: { duration: 0.2 },
                    });
                    paginate(-1);
                    xModal.set(0);
                  } else {
                    controlsModal.start({
                      x: 0,
                      transition: {
                        type: "spring",
                        stiffness: 300,
                        damping: 30,
                      },
                    });
                  }
                }}
                animate={controlsModal}
                initial={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                <div className="relative w-full h-full">
                  <Image
                    src={images[currentIndex]}
                    alt={`Enlarged image: ${altText} - Image ${
                      currentIndex + 1
                    }`}
                    fill
                    className={cn(
                      "transition-opacity duration-300",
                      loadingModal ? "opacity-0" : "opacity-100"
                    )}
                    style={{ objectFit: "contain", borderRadius: "0.5rem" }}
                    onLoad={() => handleImageLoad(currentIndex)}
                    onError={() => {
                      setLoadingModal(false);
                      if (loadingTimeoutRef.current) {
                        clearTimeout(loadingTimeoutRef.current);
                      }
                    }}
                  />
                </div>
              </motion.div>
            </AnimatePresence>
          </div>
          {!isMobile && images.length > 1 && (
            <button
              type="button"
              className="absolute right-2 top-1/2 -translate-y-1/2 z-20 bg-background/70 hover:bg-background/90 rounded-full p-2 shadow"
              onClick={() => paginate(1)}
              aria-label="Next Image"
              style={{ display: images.length > 1 ? "block" : "none" }}
            >
              <ChevronRight className="h-8 w-8" />
            </button>
          )}
          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2 z-20">
            {images.map((_, index) => (
              <button
                key={index}
                type="button"
                onClick={() => goToIndex(index)}
                aria-label={`Go to image ${index + 1}`}
                className={cn(
                  "h-3 w-3 rounded-full transition-colors border border-primary",
                  currentIndex === index
                    ? "bg-primary"
                    : "bg-muted-foreground/50 hover:bg-muted-foreground"
                )}
              />
            ))}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ImageDisplay;
