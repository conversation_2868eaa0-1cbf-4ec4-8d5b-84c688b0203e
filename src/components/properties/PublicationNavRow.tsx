"use client";
import Link from "next/link";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";

interface PublicationNavRowProps {
  prevId: string;
  nextId: string;
  desktop?: boolean;
}

const PublicationNavRow: React.FC<PublicationNavRowProps> = ({
  prevId,
  nextId,
  desktop,
}) => {
  const isMobile = useIsMobile();

  // Mobile: sticky below navbar, full width, solid bg
  if (!desktop && isMobile) {
    return (
      <div className="sticky top-[64px] z-40 bg-card border-b flex justify-between items-center px-4 py-2 shadow">
        <Link
          href={`/property/${prevId}`}
          className="text-primary font-medium flex items-center"
        >
          <ChevronLeft className="mr-1" /> Anterior
        </Link>
        <Link
          href={`/property/${nextId}`}
          className="text-primary font-medium flex items-center"
        >
          Siguiente <ChevronRight className="ml-1" />
        </Link>
      </div>
    );
  }

  // Desktop: below map
  if (desktop && !isMobile) {
    return (
      <div className="mt-4 flex justify-between items-center bg-card rounded-lg shadow px-4 py-2">
        <Link
          href={`/property/${prevId}`}
          className="text-primary font-medium flex items-center"
        >
          <ChevronLeft className="mr-1" /> Anterior
        </Link>
        <Link
          href={`/property/${nextId}`}
          className="text-primary font-medium flex items-center"
        >
          Siguiente <ChevronRight className="ml-1" />
        </Link>
      </div>
    );
  }

  return null;
};

export default PublicationNavRow;
