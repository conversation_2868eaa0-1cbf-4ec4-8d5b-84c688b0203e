import Link from "next/link";
import type { Property } from "@/types";
import { Badge } from "@/components/ui/badge";
import { MapPin } from "lucide-react";
import OptimizedImage from "@/components/ui/optimized-image";

interface PropertyCardProps {
  property: Property;
}

const PropertyCard: React.FC<PropertyCardProps> = ({ property }) => {
  const images = property.images.slice(0, 4);
  const extraCount = property.images.length - 4;
  return (
    <Link href={`/property/${property.id}`} className="block group">
      <div className="bg-card rounded-lg shadow transition overflow-hidden border border-border flex flex-col h-full transform group-hover:scale-105 group-hover:shadow-xl duration-200">
        <div className="w-full aspect-video bg-muted relative">
          {/* Facebook-style image collage */}
          <div
            className={`grid gap-0 w-full h-full ${
              images.length === 1
                ? ""
                : images.length === 2
                ? "grid-cols-2"
                : images.length === 3
                ? "grid-cols-2 grid-rows-2"
                : "grid-cols-2 grid-rows-2"
            }`}
            style={{ height: "100%", width: "100%" }}
          >
            {images.map((img, i) => (
              <div
                key={img}
                className={`relative ${
                  images.length === 3 && i === 2 ? "col-span-2 row-start-2" : ""
                } w-full h-full`}
                style={{ minHeight: 0, minWidth: 0 }}
              >
                <OptimizedImage
                  src={img}
                  alt={property.name}
                  fill
                  className="object-cover transition-transform duration-300"
                  aspectRatio="video"
                  quality={60}
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                />
                {extraCount > 0 && i === 3 && (
                  <div className="absolute inset-0 bg-black/60 flex items-center justify-center text-white text-2xl font-bold">
                    +{extraCount}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
        <div className="flex flex-col gap-2 p-4 flex-1">
          <div className="flex items-center gap-2 text-xs">
            <span className="flex items-center">
              <MapPin className="h-3 w-3 mr-1" />
              {property.location.address}
            </span>
            <span className="flex items-right">
              <Badge variant="secondary">{property.type}</Badge>
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Badge
              variant={property.available ? "default" : "destructive"}
              className={
                property.available ? "bg-green-600 hover:bg-green-700" : ""
              }
            >
              {property.available ? "Disponible" : "No disponible"}
            </Badge>
          </div>
          <div className="font-bold text-lg text-foreground">
            {property.name}
          </div>
          <div className="text-sm text-muted-foreground mb-1">
            {property.description}
          </div>
          <div className="mt-auto text-primary font-bold text-xl">
            ${property.pricePerMonth.toLocaleString("es-AR")}{" "}
            <span className="text-sm font-normal text-muted-foreground">
              / mes
            </span>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default PropertyCard;
