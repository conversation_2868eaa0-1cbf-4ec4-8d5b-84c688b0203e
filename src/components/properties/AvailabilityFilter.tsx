"use client";

import { Button } from "@/components/ui/button";

interface AvailabilityFilterProps {
  selectedAvailability: boolean | null;
  onAvailabilityChange: (availability: boolean | null) => void;
}

const AvailabilityFilter: React.FC<AvailabilityFilterProps> = ({
  selectedAvailability,
  onAvailabilityChange,
}) => {
  return (
    <div className="flex gap-2">
      <Button
        variant={selectedAvailability === null ? "default" : "outline"}
        size="sm"
        onClick={() => onAvailabilityChange(null)}
        className="whitespace-nowrap"
      >
        Todos
      </Button>
      <Button
        variant={selectedAvailability === true ? "default" : "outline"}
        size="sm"
        onClick={() => onAvailabilityChange(true)}
        className="whitespace-nowrap"
      >
        Disponible
      </Button>
      <Button
        variant={selectedAvailability === false ? "default" : "outline"}
        size="sm"
        onClick={() => onAvailabilityChange(false)}
        className="whitespace-nowrap"
      >
        No disponible
      </Button>
    </div>
  );
};

export default AvailabilityFilter;
