export type Review = {
  id: string;
  author: string;
  rating: number;
  comment: string;
  date: string;
};

export type Location = {
  lat: number;
  lng: number;
  address: string;
};

export type PropertyType = "Casa" | "Departamento" | "Galpón" | "Condominio" | "Dúplex";

export type Property = {
  id: string;
  name: string;
  type: PropertyType;
  description: string;
  images: string[];
  pricePerMonth: number; // Changed from pricePerNight
  bedrooms: number;
  bathrooms: number;
  amenities: string[];
  features: string[];
  not_allowed: string[];
  available: boolean;
  sqft?: number;
  location: Location;
  reviews: Review[];
  host: {
    name: string;
    avatar: string;
  };
};
