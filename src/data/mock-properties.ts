import type { Property } from '@/types';

export const mockProperties: Property[] = [
  {
    id: '3',
    name: 'Departamento dúplex',
    type: 'Dúplex',
    description: 'Departamento ubicado en la calle Evaristo <PERSON> 570. Cuenta con 2 dormitorios, baño completo, cocina y living comedor, balcon privado, lavandero y amplio patio compartido. Cochera p/auto en sector lavadero.',
    images: [
      'https://utfs.io/f/zdBDP0Q1NHxClKXjHXt3Tjm7ODbaXoRUh0AeI8FkQWpH1NJz',
      'https://utfs.io/f/zdBDP0Q1NHxCmuAoiv9ZX57Op0TsaN64ygFIb8PekSMVARif',
      'https://utfs.io/f/zdBDP0Q1NHxCw1hvKL9FvxEZWhAqmbgKCTz4M51RXcG6oDOk',
      'https://utfs.io/f/zdBDP0Q1NHxCaGCHWespGK1Qj4ET7pyHDO6CvhUsLi9ux2BS',
      'https://utfs.io/f/zdBDP0Q1NHxCSCRA06aaX8R9wkYe7jCQhroTzfbxVWP4s3OM',
      'https://utfs.io/f/zdBDP0Q1NHxC18bAcn45xI0TucmWaGMtC27LPVOiyJwqKzRF',
      'https://utfs.io/f/zdBDP0Q1NHxCjA4qqdnzXJGC0UHIktOn4Nw8f7vLBmlVrFyQ',
    ],
    pricePerMonth: 380000,
    bedrooms: 2,
    bathrooms: 1,
    sqft: 100,
    amenities: ['Balcon Privado', 'Habitacion con Placar', '2 Habitaciones', 'Cocina/Comedor', 'Lavadero', 'Patio Compartido', 'Cochera p/auto'],
    features: ['Termotanque', 'Capacidad Maxima: 3 Personas', 'Contrato actualizable cada 6 meses'],
    not_allowed: ['No se aceptan mascotas'],
    available: true,
    lat: -29.1387376,
    lng: -59.267436,
    location: {
      lat: -29.1387376,
      lng: -59.267436,
      address: 'Evaristo Lopez 570, Goya',
    },
    reviews: [],
    host: { name: 'Propietario', avatar: '' },
  },
  {
    id: '4',
    name: 'Amplio galpon',
    type: 'Galpón',
    description: 'Galpon ubicado sobre Ruta Provincial 27, con amplia playa de estacionamiento. Ubicado a metros del acceso principal a la ciudad de Goya.',
    images: [
      'https://utfs.io/f/zdBDP0Q1NHxC10K9eX45xI0TucmWaGMtC27LPVOiyJwqKzRF',
      'https://utfs.io/f/zdBDP0Q1NHxCjkrddBnzXJGC0UHIktOn4Nw8f7vLBmlVrFyQ',
      'https://utfs.io/f/zdBDP0Q1NHxCsiulOO31N7SgqMIAKT93f0jcExs4RYUbCLdn',
      'https://utfs.io/f/zdBDP0Q1NHxCnrhTjHYOiLmVtxCM0Dd7EGk85hfQqu4KavRc',
      'https://utfs.io/f/zdBDP0Q1NHxCFbBKm1sFduBjC8MvZNRbna6q5p9hgm1weT4L'
    ],
    pricePerMonth: 2500000,
    bedrooms: 0,
    bathrooms: 0,
    sqft: 750,
    amenities: ['2 Portones Acceso', 'Oficinas Privadas'],
    features: ['Acceso a Oficinas', 'Techos altos', 'Amplia Playa Estacionamiento'],
    not_allowed: [],
    available: true,
    lat: -29.129712,
    lng: -59.241043,
    location: {
      lat: -29.129712,
      lng: -59.241043,
      address: 'Ruta Provincial 27 - KM 126, Goya',
    },
    reviews: [],
    host: { name: 'Propietario', avatar: '' },
  },
  {
    id: '5',
    name: 'Departamento 2 ambientes',
    type: 'Departamento',
    description: 'Departamento ubicado en la calle 25 de Mayo 930. Cuenta con 1 dormitorios, baño completo, cocina/living comedor y lavandero.',
    images: [
      'https://utfs.io/f/zdBDP0Q1NHxC10K9eX45xI0TucmWaGMtC27LPVOiyJwqKzRF'
    ],
    pricePerMonth: 2500000,
    bedrooms: 0,
    bathrooms: 0,
    sqft: 750,
    amenities: ['Habitacion con Placar', '2 Habitaciones', 'Cocina/Comedor', 'Lavadero'],
    features: ['Termotanque', 'Capacidad Maxima: 2 Personas', 'Ventilador en Living', 'Split en Living', 'Contrato actualizable cada 6 meses'],
    not_allowed: ['No se aceptan mascotas'],
    available: true,
    lat: -29.1387376,
    lng: -59.267436,
    location: {
      lat: -29.1387376,
      lng: -59.267436,
      address: 'Evaristo Lopez 570, Goya',
    },
    reviews: [],
    host: { name: 'Propietario', avatar: '' },
  },
  {
    id: '6',
    name: 'Departamento 3 ambientes',
    type: 'Departamento',
    description: 'Departamento ubicado en la calle Evaristo Lopez 570. Cuenta con 1 dormitorios, baño completo, cocina, living comedor y lavandero.',
    images: [
      'https://utfs.io/f/zdBDP0Q1NHxC10K9eX45xI0TucmWaGMtC27LPVOiyJwqKzRF'
    ],
    pricePerMonth: 2500000,
    bedrooms: 0,
    bathrooms: 0,
    sqft: 750,
    amenities: ['Habitacion con Placar', 'Baño con Placar', 'Cocina', 'Comedor', 'Lavadero'],
    features: ['Termotanque', 'Capacidad Maxima: 2 Personas', 'Split en Dormitorio', 'Contrato actualizable cada 6 meses'],
    not_allowed: ['No se aceptan mascotas'],
    available: true,
    lat: -29.1387376,
    lng: -59.267436,
    location: {
      lat: -29.1387376,
      lng: -59.267436,
      address: 'Evaristo Lopez 570, Goya',
    },
    reviews: [],
    host: { name: 'Propietario', avatar: '' },
  },
];

export const getProperties = async (): Promise<Property[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  return mockProperties;
};

export const getPropertyById = async (id: string): Promise<Property | undefined> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));
  return mockProperties.find(property => property.id === id);
};
